$(document).ready(function() {
    let walletConnected = false;
    let walletAddress = null;

    checkWalletConnection();

    $("#connectWallet").click(function() {
        connectWallet();
    });

    $("#disconnectWallet").click(function() {
        disconnectWallet();
    });

    async function checkWalletConnection() {
        try {
            if (window.solana && window.solana.isPhantom) {
                const response = await window.solana.connect({ onlyIfTrusted: true });
                if (response.publicKey) {
                    walletConnected = true;
                    walletAddress = response.publicKey.toString();
                    updateWalletUI();
                }
            }
        } catch (error) {
            console.log("Wallet not connected");
        }
    }

    async function connectWallet() {
        try {
            if (window.solana && window.solana.isPhantom) {
                const response = await window.solana.connect();
                walletConnected = true;
                walletAddress = response.publicKey.toString();
                updateWalletUI();
            } else {
                $("#formError").html(`
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" fill="currentColor"/>
                        </svg>
                        <strong>Wallet not found</strong>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        Please install a Solana wallet like Phantom to continue.
                    </div>
                    <div style="font-size: 0.875rem; opacity: 0.8;">
                        <a href="https://phantom.app/" target="_blank" style="color: #14f195; text-decoration: underline;">Download Phantom Wallet</a>
                    </div>
                `).show();
            }
        } catch (error) {
            console.error("Error connecting wallet:", error);
            $("#formError").html(`
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" fill="currentColor"/>
                    </svg>
                    <strong>Connection failed. Please try again.</strong>
                </div>
            `).show();
        }
    }

    function disconnectWallet() {
        walletConnected = false;
        walletAddress = null;
        updateWalletUI();
        $("#formError").hide();
        $("#formSuccess").hide();
    }

    function updateWalletUI() {
        if (walletConnected && walletAddress) {
            $("#walletStatus").hide();
            $("#connectedWallet").show();
            $("#walletAddress").text(formatAddress(walletAddress));
            checkExistingAllocation(walletAddress);
        } else {
            $("#walletStatus").show();
            $("#connectedWallet").hide();
            $("#formError").hide();
            $("#formSuccess").hide();
        }
    }

    function checkExistingAllocation(address) {
        const allocation = generateAllocation(address);
        displayAllocationResult(allocation, true);
    }

    function getTimeAgo(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
        if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        return 'just now';
    }

    function formatAddress(address) {
        if (!address) return '';
        return `${address.slice(0, 4)}...${address.slice(-4)}`;
    }

    function generateAllocation(address) {
        const existingAllocations = JSON.parse(localStorage.getItem('jitoastAllocations') || '{}');

        if (existingAllocations[address]) {
            return existingAllocations[address];
        }

        const baseAllocation = 100000;
        const randomBonus = Math.floor(Math.random() * 500000);
        const totalAllocation = baseAllocation + randomBonus;

        const allocation = {
            allocation: totalAllocation.toLocaleString(),
            timestamp: Date.now()
        };

        existingAllocations[address] = allocation;
        localStorage.setItem('jitoastAllocations', JSON.stringify(existingAllocations));

        return allocation;
    }

    function displayAllocationResult(result, isAutomatic = false) {
        const $formSuccess = $("#formSuccess");
        const timeAgo = getTimeAgo(result.timestamp);
        const statusText = isAutomatic ? `Verified ${timeAgo}` : 'Your allocation confirmed';

        $formSuccess.html(`
            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1.5rem;">
                <svg width="24" height="24" viewBox="0 0 20 20" fill="none">
                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" fill="currentColor"/>
                </svg>
                <strong>🎉 JITOAST Airdrop Allocation</strong>
            </div>
            <div style="background: linear-gradient(135deg, rgba(20, 241, 149, 0.1), rgba(153, 69, 255, 0.1)); padding: 2rem; border-radius: 16px; text-align: center; border: 1px solid rgba(20, 241, 149, 0.2);">
                <div style="font-size: 3rem; font-weight: 900; color: #14f195; margin-bottom: 0.5rem;">
                    ${result.allocation} $JITOAST
                </div>
                <div style="font-size: 1.1rem; color: #666; font-weight: 500;">
                    ${statusText}
                </div>
            </div>
        `).show();
    }



    $(window).scroll(function() {
        const scrolled = $(this).scrollTop();
        const parallax = scrolled * 0.1;
        $(".hero-image img").css("transform", `translateY(${parallax}px)`);
    });

    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    window.clearJitoastAllocations = function() {
        localStorage.removeItem('jitoastAllocations');
        console.log('JITOAST allocations cleared from localStorage');
        location.reload();
    };

    window.viewJitoastAllocations = function() {
        const allocations = JSON.parse(localStorage.getItem('jitoastAllocations') || '{}');
        console.log('Current JITOAST allocations:', allocations);
        return allocations;
    };

    const totalAllocations = JSON.parse(localStorage.getItem('jitoastAllocations') || '{}');
    const totalCount = Object.keys(totalAllocations).length;
    if (totalCount > 0) {
        console.log(`🎯 JITOAST Airdrop Tracker: ${totalCount} wallet${totalCount > 1 ? 's' : ''} have checked allocations`);
        console.log('💡 Use clearJitoastAllocations() to reset or viewJitoastAllocations() to see all');
    }
});
