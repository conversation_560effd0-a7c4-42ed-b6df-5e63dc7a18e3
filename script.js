// Monad Airdrop Checker JavaScript

$(document).ready(function() {
    // Initialize form validation
    $("#verificationForm").validate({
        rules: {
            ethereumAddress: {
                required: true,
                minlength: 42,
                maxlength: 42,
                pattern: /^0x[a-fA-F0-9]{40}$/
            }
        },
        messages: {
            ethereumAddress: {
                required: "Please enter your Ethereum address",
                minlength: "Ethereum address must be 42 characters long",
                maxlength: "Ethereum address must be 42 characters long",
                pattern: "Please enter a valid Ethereum address (0x...)"
            }
        },
        errorElement: "label",
        errorClass: "error",
        errorPlacement: function(error, element) {
            error.insertAfter(element.parent());
        },
        highlight: function(element) {
            $(element).addClass("error");
        },
        unhighlight: function(element) {
            $(element).removeClass("error");
        },
        submitHandler: function(form) {
            checkAllocation();
            return false;
        }
    });

    // Add custom validation method for Ethereum address pattern
    $.validator.addMethod("pattern", function(value, element, regexp) {
        if (regexp.constructor != RegExp) {
            regexp = new RegExp(regexp);
        }
        return this.optional(element) || regexp.test(value);
    }, "Please enter a valid format.");

    // Check allocation function
    function checkAllocation() {
        const address = $("#ethereumAddress").val().trim();
        const $button = $(".btn-primary");
        const $buttonText = $(".btn-text");
        const $buttonLoader = $(".btn-loader");
        const $formError = $("#formError");
        const $formSuccess = $("#formSuccess");

        // Hide previous messages
        $formError.hide();
        $formSuccess.hide();

        // Show loading state
        $button.prop("disabled", true);
        $buttonText.hide();
        $buttonLoader.show();

        // Simulate API call with timeout
        setTimeout(function() {
            // Mock allocation check logic
            const mockAllocations = {
                "******************************************": {
                    eligible: true,
                    allocation: "1,000.00",
                    criteria: ["Testnet Participation", "Community Contribution"]
                },
                "******************************************": {
                    eligible: true,
                    allocation: "500.00",
                    criteria: ["Testnet Participation", "Ecosystem Development"]
                },
                "0x8ba1f109551bD432803012645Hac136c22C57B": {
                    eligible: true,
                    allocation: "250.00",
                    criteria: ["Community Contribution", "Ecosystem Development"]
                }
            };

            const result = mockAllocations[address];

            // Reset button state
            $button.prop("disabled", false);
            $buttonText.show();
            $buttonLoader.hide();

            if (result && result.eligible) {
                // Show success message
                $formSuccess.html(`
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" fill="currentColor"/>
                        </svg>
                        <strong>Congratulations! You are eligible for the Monad airdrop.</strong>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <div style="font-size: 1.5rem; font-weight: 700; color: #86efac; margin-bottom: 0.5rem;">
                            ${result.allocation} $MON
                        </div>
                        <div style="font-size: 0.875rem; opacity: 0.8;">
                            Your allocated token amount
                        </div>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">Eligibility Criteria Met:</div>
                        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                            ${result.criteria.map(criterion => 
                                `<span style="background: rgba(34, 197, 94, 0.2); color: #86efac; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">${criterion}</span>`
                            ).join('')}
                        </div>
                    </div>
                    <div style="font-size: 0.875rem; opacity: 0.8;">
                        Tokens will be claimable at Token Generation Event (TGE). Keep this address safe for claiming.
                    </div>
                `).show();
            } else {
                // Show not eligible message
                $formError.html(`
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" fill="currentColor"/>
                        </svg>
                        <strong>Address not eligible</strong>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        This Ethereum address is not eligible for the Monad airdrop based on our verification criteria.
                    </div>
                    <div style="font-size: 0.875rem; opacity: 0.8;">
                        Eligibility is based on testnet participation, community contributions, and ecosystem development activities.
                    </div>
                `).show();
            }

            // Scroll to result
            $('html, body').animate({
                scrollTop: $formError.is(':visible') ? $formError.offset().top - 100 : $formSuccess.offset().top - 100
            }, 500);

        }, 2000); // 2 second delay to simulate API call
    }

    // Input formatting and validation
    $("#ethereumAddress").on("input", function() {
        let value = $(this).val();
        
        // Auto-add 0x prefix if not present
        if (value.length > 0 && !value.startsWith("0x")) {
            value = "0x" + value;
            $(this).val(value);
        }
        
        // Remove invalid characters
        value = value.replace(/[^0-9a-fA-Fx]/g, "");
        
        // Limit length
        if (value.length > 42) {
            value = value.substring(0, 42);
        }
        
        $(this).val(value);
    });

    // Clear messages when user starts typing
    $("#ethereumAddress").on("focus", function() {
        $("#formError").hide();
        $("#formSuccess").hide();
    });

    // Animate gradient orb
    setInterval(function() {
        $(".gradient-orb").css({
            'background': `linear-gradient(${Math.random() * 360}deg, #a855f7, #6366f1, #3b82f6)`
        });
    }, 3000);

    // Add some interactive effects
    $(".info-card").hover(
        function() {
            $(this).find(".info-icon").css("transform", "scale(1.1)");
        },
        function() {
            $(this).find(".info-icon").css("transform", "scale(1)");
        }
    );

    // Smooth scrolling for any anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
});
