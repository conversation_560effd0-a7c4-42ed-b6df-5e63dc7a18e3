// JITOAST Airdrop Checker JavaScript

$(document).ready(function() {
    let walletConnected = false;
    let walletAddress = null;

    // Check if wallet is already connected
    checkWalletConnection();

    // Connect wallet button click
    $("#connectWallet").click(function() {
        connectWallet();
    });

    // Disconnect wallet button click
    $("#disconnectWallet").click(function() {
        disconnectWallet();
    });

    // Check allocation button click
    $("#checkAllocation").click(function() {
        if (walletConnected && walletAddress) {
            checkAllocation(walletAddress);
        }
    });

    // Check if wallet is already connected on page load
    async function checkWalletConnection() {
        try {
            if (window.solana && window.solana.isPhantom) {
                const response = await window.solana.connect({ onlyIfTrusted: true });
                if (response.publicKey) {
                    walletConnected = true;
                    walletAddress = response.publicKey.toString();
                    updateWalletUI();
                }
            }
        } catch (error) {
            console.log("Wallet not connected");
        }
    }

    // Connect to Solana wallet
    async function connectWallet() {
        try {
            // Check if Phantom wallet is installed
            if (window.solana && window.solana.isPhantom) {
                const response = await window.solana.connect();
                walletConnected = true;
                walletAddress = response.publicKey.toString();
                updateWalletUI();
            } else {
                // Show error if no wallet found
                $("#formError").html(`
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" fill="currentColor"/>
                        </svg>
                        <strong>Wallet not found</strong>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        Please install a Solana wallet like Phantom to continue.
                    </div>
                    <div style="font-size: 0.875rem; opacity: 0.8;">
                        <a href="https://phantom.app/" target="_blank" style="color: #14f195; text-decoration: underline;">Download Phantom Wallet</a>
                    </div>
                `).show();
            }
        } catch (error) {
            console.error("Error connecting wallet:", error);
            $("#formError").html(`
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" fill="currentColor"/>
                    </svg>
                    <strong>Connection failed. Please try again.</strong>
                </div>
            `).show();
        }
    }

    // Disconnect wallet
    function disconnectWallet() {
        walletConnected = false;
        walletAddress = null;
        updateWalletUI();
        $("#formError").hide();
        $("#formSuccess").hide();
    }

    // Update wallet UI based on connection status
    function updateWalletUI() {
        if (walletConnected && walletAddress) {
            $("#walletStatus").hide();
            $("#connectedWallet").show();
            $("#walletAddress").text(formatAddress(walletAddress));
        } else {
            $("#walletStatus").show();
            $("#connectedWallet").hide();
        }
    }

    // Format address for display
    function formatAddress(address) {
        if (!address) return '';
        return `${address.slice(0, 4)}...${address.slice(-4)}`;
    }

    // Check allocation function
    function checkAllocation(address) {
        const $button = $("#checkAllocation");
        const $buttonText = $button.find(".btn-text");
        const $buttonLoader = $button.find(".btn-loader");
        const $formError = $("#formError");
        const $formSuccess = $("#formSuccess");

        // Hide previous messages
        $formError.hide();
        $formSuccess.hide();

        // Show loading state
        $button.prop("disabled", true);
        $buttonText.hide();
        $buttonLoader.show();

        // Simulate API call with timeout
        setTimeout(function() {
            // Mock allocation check logic for Solana addresses
            const mockAllocations = {
                "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU": {
                    eligible: true,
                    allocation: "1,000.00",
                    criteria: ["Staking Participation", "Community Contribution"]
                },
                "DjVE6JNiYqPL2QXyCUUh8rNjHrbz9hXHNYt99MQ59qw1": {
                    eligible: true,
                    allocation: "500.00",
                    criteria: ["DeFi Participation", "Ecosystem Development"]
                },
                "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM": {
                    eligible: true,
                    allocation: "250.00",
                    criteria: ["NFT Trading", "Community Contribution"]
                }
            };

            const result = mockAllocations[address];

            // Reset button state
            $button.prop("disabled", false);
            $buttonText.show();
            $buttonLoader.hide();

            if (result && result.eligible) {
                // Show success message
                $formSuccess.html(`
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" fill="currentColor"/>
                        </svg>
                        <strong>Congratulations! You are eligible for the JITOAST airdrop.</strong>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <div style="font-size: 1.5rem; font-weight: 700; color: #14f195; margin-bottom: 0.5rem;">
                            ${result.allocation} $JITO
                        </div>
                        <div style="font-size: 0.875rem; opacity: 0.8;">
                            Your allocated token amount
                        </div>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">Eligibility Criteria Met:</div>
                        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                            ${result.criteria.map(criterion =>
                                `<span style="background: rgba(20, 241, 149, 0.2); color: #14f195; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">${criterion}</span>`
                            ).join('')}
                        </div>
                    </div>
                    <div style="font-size: 0.875rem; opacity: 0.8;">
                        Tokens will be claimable at Token Generation Event (TGE). Keep this address safe for claiming.
                    </div>
                `).show();
            } else {
                // Show not eligible message
                $formError.html(`
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" fill="currentColor"/>
                        </svg>
                        <strong>Address not eligible</strong>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        This Solana address is not eligible for the JITOAST airdrop based on our verification criteria.
                    </div>
                    <div style="font-size: 0.875rem; opacity: 0.8;">
                        Eligibility is based on staking participation, DeFi activities, and community contributions.
                    </div>
                `).show();
            }

            // Scroll to result
            $('html, body').animate({
                scrollTop: $formError.is(':visible') ? $formError.offset().top - 100 : $formSuccess.offset().top - 100
            }, 500);

        }, 2000); // 2 second delay to simulate API call
    }

    // Input formatting and validation for Solana addresses
    $("#solanaAddress").on("input", function() {
        let value = $(this).val();

        // Remove invalid characters (keep only base58 characters)
        value = value.replace(/[^1-9A-HJ-NP-Za-km-z]/g, "");

        // Limit length to 44 characters max
        if (value.length > 44) {
            value = value.substring(0, 44);
        }

        $(this).val(value);
    });

    // Clear messages when user starts typing
    $("#solanaAddress").on("focus", function() {
        $("#formError").hide();
        $("#formSuccess").hide();
    });

    // Add interactive effects for cards
    $(".info-card").hover(
        function() {
            $(this).find(".info-icon").css("transform", "scale(1.1) rotate(5deg)");
        },
        function() {
            $(this).find(".info-icon").css("transform", "scale(1) rotate(0deg)");
        }
    );

    // Add subtle parallax effect to hero image
    $(window).scroll(function() {
        const scrolled = $(this).scrollTop();
        const parallax = scrolled * 0.1;
        $(".hero-image img").css("transform", `translateY(${parallax}px)`);
    });

    // Smooth scrolling for any anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
});
