$(document).ready(function() {
    let walletConnected = false;
    let walletAddress = null;

    checkWalletConnection();

    $("#connectWallet").click(function() {
        connectWallet();
    });

    $("#disconnectWallet").click(function() {
        disconnectWallet();
    });

    $("#checkAllocation").click(function() {
        if (walletConnected && walletAddress) {
            checkAllocation(walletAddress);
        }
    });

    async function checkWalletConnection() {
        try {
            if (window.solana && window.solana.isPhantom) {
                const response = await window.solana.connect({ onlyIfTrusted: true });
                if (response.publicKey) {
                    walletConnected = true;
                    walletAddress = response.publicKey.toString();
                    updateWalletUI();
                }
            }
        } catch (error) {
            console.log("Wallet not connected");
        }
    }

    async function connectWallet() {
        try {
            if (window.solana && window.solana.isPhantom) {
                const response = await window.solana.connect();
                walletConnected = true;
                walletAddress = response.publicKey.toString();
                updateWalletUI();
            } else {
                $("#formError").html(`
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" fill="currentColor"/>
                        </svg>
                        <strong>Wallet not found</strong>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        Please install a Solana wallet like Phantom to continue.
                    </div>
                    <div style="font-size: 0.875rem; opacity: 0.8;">
                        <a href="https://phantom.app/" target="_blank" style="color: #14f195; text-decoration: underline;">Download Phantom Wallet</a>
                    </div>
                `).show();
            }
        } catch (error) {
            console.error("Error connecting wallet:", error);
            $("#formError").html(`
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" fill="currentColor"/>
                    </svg>
                    <strong>Connection failed. Please try again.</strong>
                </div>
            `).show();
        }
    }

    function disconnectWallet() {
        walletConnected = false;
        walletAddress = null;
        updateWalletUI();
        $("#formError").hide();
        $("#formSuccess").hide();
    }

    function updateWalletUI() {
        if (walletConnected && walletAddress) {
            $("#walletStatus").hide();
            $("#connectedWallet").show();
            $("#walletAddress").text(formatAddress(walletAddress));
            checkExistingAllocation(walletAddress);
        } else {
            $("#walletStatus").show();
            $("#connectedWallet").hide();
            $("#formError").hide();
            $("#formSuccess").hide();
        }
    }

    function checkExistingAllocation(address) {
        const existingAllocations = JSON.parse(localStorage.getItem('jitoastAllocations') || '{}');

        if (existingAllocations[address]) {
            const allocation = existingAllocations[address];
            const timeAgo = getTimeAgo(allocation.timestamp);

            $("#formSuccess").html(`
                <div style="background: rgba(20, 241, 149, 0.1); padding: 1rem; border-radius: 8px; border: 1px solid rgba(20, 241, 149, 0.2); margin-bottom: 1rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <svg width="16" height="16" viewBox="0 0 20 20" fill="none">
                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" fill="currentColor"/>
                        </svg>
                        <strong style="color: #14f195;">Existing Allocation Found</strong>
                    </div>
                    <div style="color: #666; font-size: 0.875rem;">
                        This wallet has an allocation of <strong style="color: #14f195;">${allocation.allocation} $JITO</strong>
                        (verified ${timeAgo})
                    </div>
                </div>
            `).show();
        }
    }

    function getTimeAgo(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
        if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        return 'just now';
    }

    function formatAddress(address) {
        if (!address) return '';
        return `${address.slice(0, 4)}...${address.slice(-4)}`;
    }

    function generateAllocation(address) {
        const existingAllocations = JSON.parse(localStorage.getItem('jitoastAllocations') || '{}');

        if (existingAllocations[address]) {
            return existingAllocations[address];
        }

        const allCriteria = [
            "Staking Participation",
            "DeFi Trading",
            "NFT Collections",
            "Community Governance",
            "Ecosystem Development",
            "Liquidity Provision",
            "Cross-chain Activity",
            "Early Adopter",
            "High Volume Trading",
            "Long-term Holder"
        ];

        const numCriteria = Math.floor(Math.random() * 3) + 2;
        const selectedCriteria = [];
        const shuffled = [...allCriteria].sort(() => 0.5 - Math.random());

        for (let i = 0; i < numCriteria; i++) {
            selectedCriteria.push(shuffled[i]);
        }

        const baseAllocation = 100000;
        const criteriaMultiplier = selectedCriteria.length * 50000;
        const randomBonus = Math.floor(Math.random() * 200000);

        const totalAllocation = baseAllocation + criteriaMultiplier + randomBonus;

        const allocation = {
            eligible: true,
            allocation: totalAllocation.toLocaleString(),
            criteria: selectedCriteria,
            timestamp: Date.now()
        };

        existingAllocations[address] = allocation;
        localStorage.setItem('jitoastAllocations', JSON.stringify(existingAllocations));

        return allocation;
    }

    function checkAllocation(address) {
        const $button = $("#checkAllocation");
        const $buttonText = $button.find(".btn-text");
        const $buttonLoader = $button.find(".btn-loader");
        const $formError = $("#formError");
        const $formSuccess = $("#formSuccess");

        $formError.hide();
        $formSuccess.hide();

        $button.prop("disabled", true);
        $buttonText.hide();
        $buttonLoader.show();

        setTimeout(function() {
            const result = generateAllocation(address);

            $button.prop("disabled", false);
            $buttonText.show();
            $buttonLoader.hide();
            $formSuccess.html(`
                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1.5rem;">
                    <svg width="24" height="24" viewBox="0 0 20 20" fill="none">
                        <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" fill="currentColor"/>
                    </svg>
                    <strong>🎉 Congratulations! You are eligible for the JITOAST airdrop.</strong>
                </div>
                <div style="background: linear-gradient(135deg, rgba(20, 241, 149, 0.1), rgba(153, 69, 255, 0.1)); padding: 1.5rem; border-radius: 12px; margin-bottom: 1.5rem; border: 1px solid rgba(20, 241, 149, 0.2);">
                    <div style="text-align: center; margin-bottom: 1rem;">
                        <div style="font-size: 2.5rem; font-weight: 800; color: #14f195; margin-bottom: 0.5rem;">
                            ${result.allocation} $JITO
                        </div>
                        <div style="font-size: 1rem; color: #666; font-weight: 500;">
                            Your allocated token amount
                        </div>
                    </div>
                </div>
                <div style="margin-bottom: 1.5rem;">
                    <div style="font-weight: 600; margin-bottom: 1rem; font-size: 1.1rem;">🏆 Eligibility Criteria Met:</div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 0.75rem;">
                        ${result.criteria.map(criterion =>
                            `<div style="background: rgba(20, 241, 149, 0.15); color: #14f195; padding: 0.75rem 1rem; border-radius: 8px; font-size: 0.875rem; font-weight: 500; text-align: center; border: 1px solid rgba(20, 241, 149, 0.3);">
                                ✓ ${criterion}
                            </div>`
                        ).join('')}
                    </div>
                </div>
                <div style="background: rgba(20, 241, 149, 0.05); padding: 1rem; border-radius: 8px; border-left: 4px solid #14f195;">
                    <div style="font-size: 0.9rem; color: #666; line-height: 1.6;">
                        <strong>📅 Next Steps:</strong> Tokens will be claimable at Token Generation Event (TGE).
                        Your allocation is now saved and linked to your wallet address.
                        <br><br>
                        <strong>🔒 Security:</strong> Keep this wallet address safe for claiming your tokens.
                    </div>
                </div>
            `).show();

            $('html, body').animate({
                scrollTop: $formError.is(':visible') ? $formError.offset().top - 100 : $formSuccess.offset().top - 100
            }, 500);

        }, 2000);
    }

    $(window).scroll(function() {
        const scrolled = $(this).scrollTop();
        const parallax = scrolled * 0.1;
        $(".hero-image img").css("transform", `translateY(${parallax}px)`);
    });

    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    window.clearJitoastAllocations = function() {
        localStorage.removeItem('jitoastAllocations');
        console.log('JITOAST allocations cleared from localStorage');
        location.reload();
    };

    window.viewJitoastAllocations = function() {
        const allocations = JSON.parse(localStorage.getItem('jitoastAllocations') || '{}');
        console.log('Current JITOAST allocations:', allocations);
        return allocations;
    };

    const totalAllocations = JSON.parse(localStorage.getItem('jitoastAllocations') || '{}');
    const totalCount = Object.keys(totalAllocations).length;
    if (totalCount > 0) {
        console.log(`🎯 JITOAST Airdrop Tracker: ${totalCount} wallet${totalCount > 1 ? 's' : ''} have checked allocations`);
        console.log('💡 Use clearJitoastAllocations() to reset or viewJitoastAllocations() to see all');
    }
});
