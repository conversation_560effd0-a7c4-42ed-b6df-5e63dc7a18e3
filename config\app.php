<?php
/**
 * Application Configuration
 * Monad Airdrop Checker - P<PERSON> Clone
 */

return [
    'app_name' => 'Monad Airdrop Checker',
    'app_url' => $_ENV['APP_URL'] ?? 'http://localhost',
    'debug' => $_ENV['APP_DEBUG'] ?? false,
    'timezone' => 'UTC',
    
    // Rate limiting configuration
    'rate_limit' => [
        'requests_per_minute' => 10,
        'requests_per_hour' => 100,
        'block_duration' => 300, // seconds
    ],
    
    // Blockchain configuration
    'blockchain' => [
        'rpc_urls' => [
            'https://mainnet.infura.io/v3/' . ($_ENV['INFURA_PROJECT_ID'] ?? 'YOUR_PROJECT_ID'),
            'https://eth-mainnet.alchemyapi.io/v2/' . ($_ENV['ALCHEMY_API_KEY'] ?? 'YOUR_API_KEY'),
        ],
        'contract_address' => '******************************************',
        'network' => 'mainnet',
    ],
    
    // Security settings
    'security' => [
        'csrf_protection' => true,
        'input_validation' => true,
        'rate_limiting' => true,
    ],
    
    // Airdrop settings
    'airdrop' => [
        'total_supply' => '1000000000',
        'start_date' => '2025-01-01 00:00:00',
        'end_date' => '2025-12-31 23:59:59',
        'token_symbol' => 'MON',
        'token_name' => 'Monad',
    ]
];
