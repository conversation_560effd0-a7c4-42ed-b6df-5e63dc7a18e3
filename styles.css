:root {
    --primary: #14f195;
    --primary-dark: #0dd186;
    --secondary: #9945ff;
    --background: #ffffff;
    --surface: #f8f9fa;
    --surface-light: #ffffff;
    --text-primary: #1a1a1a;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border: #e5e7eb;
    --shadow: rgba(20, 241, 149, 0.15);
    --gradient-primary: linear-gradient(135deg, #14f195 0%, #9945ff 100%);
    --gradient-surface: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    min-height: 100vh;
    overflow-x: hidden;
}

.header {
    position: relative;
    z-index: 1000;
}

.live-banner {
    background: var(--gradient-primary);
    color: white;
    text-align: center;
    padding: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.live-indicator {
    display: inline-block;
    background: #ff4757;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    margin-right: 0.75rem;
    font-size: 0.75rem;
    font-weight: 700;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.main {
    flex: 1;
}

.hero {
    padding: 6rem 2rem;
    min-height: 90vh;
    display: flex;
    align-items: center;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(20, 241, 149, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 2rem;
    color: var(--text-primary);
    letter-spacing: -0.02em;
}

.text-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    line-height: 1.7;
    max-width: 500px;
}

.verification-form {
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-input {
    flex: 1;
    padding: 1.25rem 1.5rem;
    border: 2px solid var(--border);
    border-radius: 12px;
    background: var(--surface-light);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: 'JetBrains Mono', monospace;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-input::placeholder {
    color: var(--text-muted);
    font-family: 'Inter', sans-serif;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary);
    background: white;
    box-shadow: 0 0 0 4px rgba(20, 241, 149, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1.25rem 2.5rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-width: 180px;
    box-shadow: 0 8px 24px var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(20, 241, 149, 0.2);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.form-error {
    background: rgba(255, 71, 87, 0.1);
    border: 1px solid rgba(255, 71, 87, 0.2);
    color: #ff6b7a;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-top: 1.5rem;
    backdrop-filter: blur(20px);
}

.form-success {
    background: rgba(20, 241, 149, 0.1);
    border: 1px solid rgba(20, 241, 149, 0.2);
    color: var(--primary);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-top: 1.5rem;
    backdrop-filter: blur(20px);
}

.security-note {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.hero-image {
    position: relative;
    width: 400px;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    border-radius: 24px;
    opacity: 0.1;
    filter: blur(40px);
    animation: glow 4s ease-in-out infinite alternate;
}

.hero-image img {
    width: 300px;
    height: 300px;
    object-fit: contain;
    filter: blur(1px) brightness(0.9);
    border-radius: 20px;
    position: relative;
    z-index: 1;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(1deg); }
}

@keyframes glow {
    0% { opacity: 0.1; transform: scale(1); }
    100% { opacity: 0.2; transform: scale(1.05); }
}



.footer {
    background: var(--surface);
    border-top: 1px solid var(--border);
    padding: 3rem 2rem;
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 1.125rem;
    color: var(--text-primary);
}

.footer-logo img {
    width: 28px;
    height: 28px;
    object-fit: contain;
}

.footer-text {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.footer-links {
    display: flex;
    gap: 2.5rem;
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: var(--text-primary);
}

.error {
    border-color: #ff4757 !important;
    background: rgba(255, 71, 87, 0.1) !important;
}

label.error {
    color: #ff6b7a;
    font-size: 0.875rem;
    margin-top: 0.75rem;
    display: block;
    font-weight: 500;
}

@media (max-width: 1024px) {
    .hero-container {
        gap: 4rem;
    }

    .hero-image {
        width: 300px;
        height: 300px;
    }

    .hero-image img {
        width: 240px;
        height: 240px;
    }
}

@media (max-width: 768px) {
    .hero {
        padding: 4rem 1.5rem;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .form-group {
        flex-direction: column;
    }

    .btn-primary {
        width: 100%;
    }

    .footer-container {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-links {
        gap: 1.5rem;
    }

    .hero-image {
        width: 250px;
        height: 250px;
    }

    .hero-image img {
        width: 200px;
        height: 200px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 3rem 1rem;
    }

    .footer {
        padding: 2rem 1rem;
    }
}
