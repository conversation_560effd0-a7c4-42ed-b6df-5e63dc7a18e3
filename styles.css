/* Monad Airdrop Checker Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Header Styles */
.header {
    position: relative;
    z-index: 1000;
}

.nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: #1f2937;
}

.nav-links {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #6366f1;
}

.nav-link-primary {
    background: #6366f1;
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: background 0.3s ease;
}

.nav-link-primary:hover {
    background: #5855eb;
}

.live-banner {
    background: linear-gradient(90deg, #6366f1, #8b5cf6);
    color: white;
    text-align: center;
    padding: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.live-indicator {
    display: inline-block;
    background: #ef4444;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-right: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Main Content */
.main {
    flex: 1;
}

/* Hero Section */
.hero {
    padding: 4rem 2rem;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: white;
}

.text-primary {
    color: #a855f7;
}

.hero-description {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.7;
}

/* Form Styles */
.verification-form {
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-input:focus {
    outline: none;
    border-color: #a855f7;
    background: rgba(255, 255, 255, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #a855f7, #6366f1);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-width: 160px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(168, 85, 247, 0.3);
}

.btn-primary:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.form-error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #fca5a5;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
}

.form-success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #86efac;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
}

.security-note {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.gradient-orb {
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, #a855f7, #6366f1, #3b82f6);
    border-radius: 50%;
    position: relative;
    animation: float 6s ease-in-out infinite;
    box-shadow: 0 20px 60px rgba(168, 85, 247, 0.4);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Info Section */
.info-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 4rem 2rem;
    backdrop-filter: blur(10px);
}

.info-container {
    max-width: 1200px;
    margin: 0 auto;
}

.info-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #1f2937;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.info-card {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
}

.info-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #a855f7, #6366f1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
}

.info-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.info-card-description {
    color: #6b7280;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: rgba(31, 41, 55, 0.95);
    color: white;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.25rem;
}

.footer-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: white;
}

/* Validation Error Styles */
.error {
    border-color: #ef4444 !important;
    background: rgba(239, 68, 68, 0.1) !important;
}

label.error {
    color: #fca5a5;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 1rem;
    }

    .nav-links {
        display: none;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
    }

    .form-group {
        flex-direction: column;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .footer-container {
        flex-direction: column;
        text-align: center;
    }

    .gradient-orb {
        width: 200px;
        height: 200px;
    }
}
