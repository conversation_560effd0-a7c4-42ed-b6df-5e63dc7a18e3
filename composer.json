{"name": "monad-checker/airdrop-checker", "description": "PHP clone of checkermonad.xyz - Monad airdrop verification portal", "type": "project", "license": "MIT", "require": {"php": ">=7.4", "ext-pdo": "*", "ext-json": "*", "ext-curl": "*"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "autoload": {"psr-4": {"MonadChecker\\": "src/"}}, "autoload-dev": {"psr-4": {"MonadChecker\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "serve": "php -S localhost:8000 -t public"}, "config": {"optimize-autoloader": true, "sort-packages": true}}