-- Monad Airdrop Checker Database Schema
-- Created for PHP clone of checkermonad.xyz

-- Create database
CREATE DATABASE IF NOT EXISTS monad_airdrop_checker;
USE monad_airdrop_checker;

-- Table for storing airdrop allocations
CREATE TABLE airdrop_allocations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ethereum_address VARCHAR(42) NOT NULL UNIQUE,
    allocation_amount DECIMAL(20, 8) NOT NULL DEFAULT 0,
    is_eligible BOOLEAN NOT NULL DEFAULT FALSE,
    eligibility_criteria JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ethereum_address (ethereum_address),
    INDEX idx_is_eligible (is_eligible)
);

-- Table for tracking verification attempts
CREATE TABLE verification_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ethereum_address VARCHAR(42) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    verification_result ENUM('eligible', 'not_eligible', 'invalid_address', 'error') NOT NULL,
    allocation_found DECIMAL(20, 8) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_ethereum_address (ethereum_address),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at)
);

-- Table for rate limiting
CREATE TABLE rate_limits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    request_count INT NOT NULL DEFAULT 1,
    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_request TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_blocked BOOLEAN DEFAULT FALSE,
    block_until TIMESTAMP NULL,
    PRIMARY KEY (id),
    UNIQUE KEY unique_ip (ip_address),
    INDEX idx_ip_address (ip_address),
    INDEX idx_window_start (window_start)
);

-- Table for system configuration
CREATE TABLE system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default configuration values
INSERT INTO system_config (config_key, config_value, description) VALUES
('rate_limit_requests_per_minute', '10', 'Maximum requests per minute per IP'),
('rate_limit_requests_per_hour', '100', 'Maximum requests per hour per IP'),
('rate_limit_block_duration', '300', 'Block duration in seconds for rate limit violations'),
('total_airdrop_supply', '1000000000', 'Total MON tokens allocated for airdrop'),
('airdrop_start_date', '2025-01-01 00:00:00', 'Airdrop verification start date'),
('airdrop_end_date', '2025-12-31 23:59:59', 'Airdrop verification end date'),
('blockchain_rpc_url', 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID', 'Ethereum RPC endpoint'),
('contract_address', '******************************************', 'Monad token contract address');

-- Sample airdrop allocation data (for testing)
INSERT INTO airdrop_allocations (ethereum_address, allocation_amount, is_eligible, eligibility_criteria) VALUES
('******************************************', 1000.00000000, TRUE, '{"testnet_participation": true, "community_contribution": true, "ecosystem_development": false}'),
('******************************************', 500.00000000, TRUE, '{"testnet_participation": true, "community_contribution": false, "ecosystem_development": true}'),
('0x8ba1f109551bD432803012645Hac136c22C57B', 250.00000000, TRUE, '{"testnet_participation": false, "community_contribution": true, "ecosystem_development": true}'),
('******************************************', 0.00000000, FALSE, '{"testnet_participation": false, "community_contribution": false, "ecosystem_development": false}');

-- Create indexes for better performance
CREATE INDEX idx_allocations_amount ON airdrop_allocations(allocation_amount);
CREATE INDEX idx_verification_result ON verification_attempts(verification_result);
CREATE INDEX idx_rate_limits_blocked ON rate_limits(is_blocked);
