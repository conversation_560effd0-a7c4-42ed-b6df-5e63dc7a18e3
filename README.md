# Monad Airdrop Checker - P<PERSON> Clone

A simple, clean PHP clone of the checkermonad.xyz website for verifying Monad airdrop allocations.

## Features

- **Clean UI**: Modern, responsive design matching the original website
- **Address Validation**: Real-time Ethereum address validation using jQuery Validation
- **Mock Allocation Check**: Simulated airdrop allocation verification
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Interactive Elements**: Smooth animations and hover effects

## Files Structure

```
/
├── index.html          # Main HTML page
├── styles.css          # CSS styles
├── script.js           # JavaScript functionality
├── jquery.validation-1.19.5.min.js  # jQuery validation library
└── README.md           # This file
```

## Setup Instructions

1. **Download/Clone the files** to your web server directory (e.g., `c:\xampp\htdocs\solanatop\`)

2. **Ensure you have the jQuery Validation library**:
   - The `jquery.validation-1.19.5.min.js` file should be in the same directory as `index.html`

3. **Start your web server**:
   - If using XAMPP: Start Apache from the XAMPP Control Panel
   - If using other servers: Ensure your web server is running

4. **Access the website**:
   - Open your browser and go to `http://localhost/solanatop/` (adjust path as needed)
   - Or directly open `index.html` in your browser

## How It Works

### Address Validation
- Automatically adds "0x" prefix if missing
- Validates Ethereum address format (42 characters, hexadecimal)
- Real-time input formatting and validation

### Mock Allocation Data
The application includes sample addresses for testing:

- `******************************************` - 1,000 MON (Eligible)
- `******************************************` - 500 MON (Eligible)  
- `0x8ba1f109551bD432803012645Hac136c22C57B` - 250 MON (Eligible)
- Any other address - Not eligible

### Features Included
- ✅ Responsive header with navigation
- ✅ Hero section with address input form
- ✅ Real-time form validation
- ✅ Loading states and animations
- ✅ Success/error message display
- ✅ Information cards section
- ✅ Professional footer
- ✅ Mobile-responsive design

## Customization

### Adding Real Blockchain Integration
To connect to real blockchain data, modify the `checkAllocation()` function in `script.js`:

1. Replace the mock data with actual API calls
2. Integrate with Ethereum RPC endpoints
3. Add proper error handling for network requests

### Styling Changes
- Modify `styles.css` to change colors, fonts, or layout
- Update the gradient colors in the CSS variables
- Adjust responsive breakpoints as needed

### Adding More Features
- Add wallet connection (MetaMask integration)
- Implement real-time blockchain queries
- Add more detailed allocation information
- Include claim functionality

## Browser Compatibility

- Chrome/Edge: Full support
- Firefox: Full support  
- Safari: Full support
- Mobile browsers: Responsive design included

## Dependencies

- jQuery 3.6.0 (loaded from CDN)
- jQuery Validation Plugin 1.19.5 (included locally)

## License

This is a demonstration project created as a clone of checkermonad.xyz for educational purposes.
